from task_00_intro import generate_invitations

with open('template.txt', 'r') as file:
    template_content = file.read()
    
    
attendees = [
    {"name": "<PERSON>", "event_title": "Python Conference", "event_date": "2023-07-15", "event_location": "New York"},
    {"name": "<PERSON>", "event_title": "Data Science Workshop", "event_date": "2023-08-20", "event_location": "San Francisco"},
    {"name": "<PERSON>", "event_title": "AI Summit", "event_date": None, "event_location": "Boston"}
]
generate_invitations(template_content, attendees)