The ``add_integer`` module
==========================

Using ``add_integer``
---------------------

First import ``add_integer`` from the ``0-add_integer`` file:

    >>> add = __import__('0-add_integer').add_integer

Basic tests:

    >>> add(1, 2)
    3

    >>> add(100, -2)
    98

    >>> add(2)
    100

    >>> add(100.3, -2)
    98

    >>> add(3.7, 2.1)
    5

    >>> add(0, 0)
    0

    >>> add(0)
    98

Test - a is not int

    >>> add("haha", 1)
    Traceback (most recent call last):
    ...
    TypeError: a must be an integer

Test - b is not int

    >>> add(1, "haha")
    Traceback (most recent call last):
    ...
    TypeError: b must be an integer

Test - a is None

    >>> add(None)
    Traceback (most recent call last):
    ...
    TypeError: a must be an integer

Test - b is a list

    >>> add(2, [5])
    Traceback (most recent call last):
    ...
    TypeError: b must be an integer

Test - both are negative

    >>> add(-100, -200)
    -300

Test - float('inf') as b

    >>> add(1, float("inf"))
    Traceback (most recent call last):
    ...
    OverflowError: cannot convert float infinity to integer

Test - float('nan') as b

    >>> add(1, float("nan"))
    Traceback (most recent call last):
    ...
    ValueError: cannot convert float NaN to integer

Test - missing both arguments

    >>> add()
    Traceback (most recent call last):
    ...
    TypeError: add_integer() missing 1 required positional argument: 'a'
