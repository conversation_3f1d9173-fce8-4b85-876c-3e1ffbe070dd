>>> say_my_name = __import__('3-say_my_name').say_my_name
>>> say_my_name("<PERSON>", "<PERSON>")
My name is <PERSON>
>>> say_my_name("<PERSON>", "<PERSON>")
My name is <PERSON>
>>> say_my_name("<PERSON>")
My name is <PERSON> 
>>> say_my_name("", "")
My name is  
>>> say_my_name(12, "<PERSON>")
Traceback (most recent call last):
...
TypeError: first_name must be a string
>>> say_my_name("<PERSON>", None)
Traceback (most recent call last):
...
TypeError: last_name must be a string
>>> say_my_name()
<PERSON><PERSON> (most recent call last):
...
TypeError: say_my_name() missing 1 required positional argument: 'first_name'
