The ``text_indentation`` module
===============================

Using ``text_indentation``
---------------------------

First import ``text_indentation`` from the ``5-text_indentation`` file:

    >>> text_indentation = __import__('5-text_indentation').text_indentation

Test - typical case with punctuation and spacing:
    >>> text_indentation("Hello.   How are you? Fine:   then go.")
    Hello.
    <BLANKLINE>
    How are you?
    <BLANKLINE>
    Fine:
    <BLANKLINE>
    then go.
    <BLANKLINE>

Test - with no ".", "?" and ":":
    >>> text_indentation("This is a test with no punctuation")
    This is a test with no punctuation

Test - leading and trailing spaces:
    >>> text_indentation("   Hi there.  What's up?   ")
    Hi there.
    <BLANKLINE>
    What's up?
    <BLANKLINE>

Test - multiple punctuation without space:
    >>> text_indentation("One.Two?Three:Four.")
    One.
    <BLANKLINE>
    Two?
    <BLANKLINE>
    Three:
    <BLANKLINE>
    Four.
    <BLANKLINE>

Test - not a string:
    >>> text_indentation(42)
    Traceback (most recent call last):
    ...
    TypeError: text must be a string

Test - empty string:
    >>> text_indentation("")    # no output
