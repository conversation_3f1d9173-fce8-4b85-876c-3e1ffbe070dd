>>> BaseGeometry = __import__('7-base_geometry').BaseGeometry
>>> bg = BaseGeometry()
>>> bg.integer_validator("my_int", 12)
>>> bg.integer_validator("width", 89)
>>> try:
...     bg.integer_validator("name","<PERSON>")
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__,e))
[TypeError] name must be an integer
>>> try:
...     bg.integer_validator("age",0)
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__,e))
[ValueError] age must be greater than 0
>>> try:
...     bg.integer_validator("distance", -4)
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[ValueError] distance must be greater than 0
>>> try:
...     bg.integer_validator("age", [3, 4])
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] age must be an integer

>>> try:
...     bg.integer_validator("age", None)
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] age must be an integer

>>> try:
...     bg.integer_validator("age", [3])
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] age must be an integer

>>> try:
...     bg.integer_validator("age", [4,])
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] age must be an integer

>>> try:
...     bg.integer_validator("age", True)
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] age must be an integer
>>> try:
...     bg.integer_validator("age")
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] integer_validator() missing 1 required positional argument: 'value'
>>> try:
...     bg.integer_validator("age", (4,))
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] age must be an integer
>>> try:
...     bg.integer_validator("age", {3, 4})
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] age must be an integer
>>> try:
...     bg.area()
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[Exception] area() is not implemented
>>> try:
...     bg.integer_validator()
... except Exception as e:
...     print("[{}] {}".format(e.__class__.__name__, e))
[TypeError] integer_validator() missing 2 required positional arguments: 'name' and 'value'
